<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clever - نظام إدارة المبيعات الشامل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        /* Header */
        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 5px;
        }

        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8rem 2rem 4rem;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .hero-content {
            text-align: right;
        }

        .hero-image {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .hero-image img {
            max-width: 100%;
            height: auto;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }

        .hero-image img:hover {
            transform: scale(1.05);
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease 0.2s both;
        }

        .cta-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 1rem 2.5rem;
            font-size: 1.2rem;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(238, 90, 36, 0.4);
            animation: fadeInUp 1s ease 0.4s both;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(238, 90, 36, 0.6);
        }

        /* Features Section */
        .features {
            padding: 6rem 2rem;
            background: #f8f9fa;
            position: relative;
        }

        .features::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100px;
            background: linear-gradient(to bottom, #667eea, transparent);
            opacity: 0.1;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #2c3e50;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .feature-card {
            background: white;
            padding: 2.5rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .feature-card p {
            color: #666;
            line-height: 1.8;
        }

        /* Screenshot Section */
        .screenshot {
            padding: 6rem 2rem;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            text-align: center;
        }

        .screenshot-container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .screenshot img {
            max-width: 100%;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            margin-top: 3rem;
            transition: transform 0.3s ease;
        }

        .screenshot img:hover {
            transform: scale(1.05);
        }

        /* Pricing Section */
        .pricing {
            padding: 6rem 2rem;
            background: #f8f9fa;
        }

        .pricing-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .pricing-card {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-top: 3rem;
            position: relative;
            overflow: hidden;
        }

        .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .price {
            font-size: 3rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .pricing-features {
            list-style: none;
            margin: 2rem 0;
        }

        .pricing-features li {
            padding: 0.5rem 0;
            color: #666;
            position: relative;
            padding-right: 2rem;
        }

        .pricing-features li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: #27ae60;
            font-weight: bold;
        }

        /* Contact Section */
        .contact {
            padding: 6rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .contact-container {
            max-width: 600px;
            margin: 0 auto;
        }

        .contact-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 15px;
            margin-top: 3rem;
            backdrop-filter: blur(10px);
        }

        .contact-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin: 1rem 0;
            font-size: 1.1rem;
        }

        /* Footer */
        footer {
            background: #2c3e50;
            color: white;
            padding: 2rem;
            text-align: center;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.8s ease forwards;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .nav-links {
                display: none;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .hero {
                padding: 6rem 1rem 3rem;
            }

            .hero-container {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .hero-content {
                text-align: center;
                order: 2;
            }

            .hero-image {
                order: 1;
            }
        }

        /* Floating Elements */
        .floating {
            position: absolute;
            animation: float 6s ease-in-out infinite;
        }

        .floating:nth-child(1) {
            animation-delay: 0s;
        }

        .floating:nth-child(2) {
            animation-delay: 2s;
        }

        .floating:nth-child(3) {
            animation-delay: 4s;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <header>
        <nav>
            <div class="logo">
                <img src="logo.png" alt="Clever Logo">
                Clever
            </div>
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">المميزات</a></li>
                <li><a href="#pricing">الأسعار</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-container">
            <div class="hero-content">
                <h1>نظام إدارة المبيعات الشامل</h1>
                <p>حل متكامل لإدارة مبيعاتك، من تتبع المخزون إلى إدارة العملاء وتحليل الأرباح</p>
                <button class="cta-button">احصل على النسخة التجريبية</button>
            </div>
            <div class="hero-image">
                <img src="hero-image.png" alt="نظام إدارة المبيعات" />
            </div>
        </div>
        <div class="floating" style="top: 20%; left: 10%; font-size: 2rem;">📊</div>
        <div class="floating" style="top: 60%; right: 15%; font-size: 1.5rem;">💰</div>
        <div class="floating" style="top: 40%; left: 80%; font-size: 1.8rem;">📈</div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="features-container">
            <h2 class="section-title">مميزات التطبيق</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <span class="feature-icon">📊</span>
                    <h3>أرباح المبيعات</h3>
                    <p>تتبع دقيق لأرباحك مع تقارير مفصلة وتحليلات شاملة لأداء مبيعاتك</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">💰</span>
                    <h3>رصيد الخزينة</h3>
                    <p>إدارة كاملة للخزينة مع تتبع المدفوعات والمقبوضات بدقة عالية</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">📦</span>
                    <h3>إدارة المبيعات</h3>
                    <p>نظام شامل لإدارة عمليات البيع من الطلب حتى التسليم</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">👥</span>
                    <h3>حسابات الزبائن</h3>
                    <p>إدارة شاملة لحسابات العملاء مع تتبع المديونيات والمدفوعات</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🎁</span>
                    <h3>إضافة منتج</h3>
                    <p>إضافة وإدارة المنتجات بسهولة مع تفاصيل شاملة لكل منتج</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🛒</span>
                    <h3>حد طلب المبيعات</h3>
                    <p>تحديد حدود الطلب وإدارة المخزون بذكاء لتجنب النقص</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">📋</span>
                    <h3>المرتجعات</h3>
                    <p>نظام فعال لإدارة مرتجعات البضائع وتسوية الحسابات</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🛍️</span>
                    <h3>إدارة المشتريات</h3>
                    <p>إدارة شاملة لعمليات الشراء من الموردين مع تتبع الفواتير</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">👨‍💼</span>
                    <h3>حسابات الموردين</h3>
                    <p>إدارة علاقات الموردين مع تتبع المستحقات والمدفوعات</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🔧</span>
                    <h3>صلاحيات المستخدم</h3>
                    <p>نظام متقدم لإدارة صلاحيات المستخدمين وتأمين البيانات</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Screenshot Section -->
    <section class="screenshot">
        <div class="screenshot-container">
            <h2 class="section-title" style="color: white;">واجهة سهلة الاستخدام</h2>
            <p style="font-size: 1.2rem; opacity: 0.9;">تصميم عصري ومتطور يجعل إدارة أعمالك أمراً سهلاً ومريحاً</p>
            <div
                style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 15px; margin-top: 3rem; backdrop-filter: blur(10px);">
                <p style="font-size: 1.1rem;">🖥️ واجهة سطح مكتب متطورة</p>
                <p style="margin-top: 1rem;">📱 متوافق مع جميع أحجام الشاشات</p>
                <p style="margin-top: 1rem;">⚡ أداء سريع وموثوق</p>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing" id="pricing">
        <div class="pricing-container">
            <h2 class="section-title">خطط الأسعار</h2>
            <div class="pricing-card">
                <div class="price">اتصل للحصول على السعر</div>
                <p style="color: #666; margin-bottom: 2rem;">حلول مخصصة حسب احتياجات عملك</p>
                <ul class="pricing-features">
                    <li>جميع المميزات المتاحة</li>
                    <li>دعم فني مجاني</li>
                    <li>تدريب على استخدام النظام</li>
                    <li>تخصيص حسب الطلب</li>
                    <li>تحديثات مجانية</li>
                    <li>نسخ احتياطي آمن</li>
                </ul>
                <button class="cta-button">احصل على عرض سعر</button>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="control">
        <div class="contact-container">
            <h2 class="section-title" style="color: white;">تواصل معنا</h2>
            <p style="font-size: 1.2rem; opacity: 0.9;">نحن هنا لمساعدتك في تطوير أعمالك</p>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📞</span>
                    <span>08 32 21 57 06</span>
                </div>
                <div class="contact-item">
                    <span>📧</span>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>📍</span>
                    <span>قسنطينة، الجزائر</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <p>&copy; 2025 Clever - جميع الحقوق محفوظة</p>
        <p style="margin-top: 0.5rem; opacity: 0.7;">تم التطوير ب في الجزائر</p>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to feature cards
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, observerOptions);

        // Observe all feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            observer.observe(card);
        });

        // Add click handlers for CTA buttons
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('click', function () {
                // Add a subtle animation effect
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);

                // Here you could add functionality to open a contact form
                // or redirect to a signup page
                alert('شكراً لاهتمامك! سيتم التواصل معك قريباً.');
            });
        });

        // Add parallax effect to hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero');
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        });
    </script>
</body>

</html>